#!/usr/bin/env python3
"""
PCI规划系统Web前端
支持文件上传、数据处理、结果下载和可视化展示
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, request, jsonify, render_template, send_file, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
import pandas as pd
from threading import Thread

# 导入超高性能PCI规划系统
from ultra_fast_pci_system import UltraFastPCISystem
# 导入Word报告生成器
from pci_report_generator import generate_pci_analysis_report

app = Flask(__name__)
CORS(app)

# 配置
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'
ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'xls'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)
os.makedirs('static', exist_ok=True)
os.makedirs('templates', exist_ok=True)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULTS_FOLDER'] = RESULTS_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
app.config['JSON_AS_ASCII'] = False  # 支持中文JSON输出

# 任务队列
task_queue = {}

def allowed_file(filename):
    """检查文件类型"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_files():
    """上传文件接口"""
    try:
        if 'cell_file' not in request.files:
            return jsonify({'error': '请选择小区数据文件'}), 400
        
        cell_file = request.files['cell_file']
        neighbor_file = request.files.get('neighbor_file')
        planning_mode = request.form.get('planning_mode', 'problem_only')

        if cell_file.filename == '':
            return jsonify({'error': '请选择小区数据文件'}), 400
        
        if not allowed_file(cell_file.filename):
            return jsonify({'error': '不支持的文件格式，请上传CSV或Excel文件'}), 400
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 保存文件
        cell_filename = secure_filename(f"{task_id}_cells.{cell_file.filename.rsplit('.', 1)[1]}")
        cell_filepath = os.path.join(app.config['UPLOAD_FOLDER'], cell_filename)
        cell_file.save(cell_filepath)
        
        neighbor_filepath = None
        if neighbor_file and neighbor_file.filename != '':
            if allowed_file(neighbor_file.filename):
                neighbor_filename = secure_filename(f"{task_id}_neighbors.{neighbor_file.filename.rsplit('.', 1)[1]}")
                neighbor_filepath = os.path.join(app.config['UPLOAD_FOLDER'], neighbor_filename)
                neighbor_file.save(neighbor_filepath)
        
        # 创建任务记录
        task_queue[task_id] = {
            'task_id': task_id,
            'status': 'uploaded',
            'progress': 0,
            'message': '文件上传成功',
            'created_at': datetime.now().isoformat(),
            'cell_file': cell_filepath,
            'neighbor_file': neighbor_filepath,
            'planning_mode': planning_mode,
            'cell_count': 0,
            'results': None
        }
        
        return jsonify({
            'task_id': task_id,
            'status': 'uploaded',
            'message': '文件上传成功',
            'planning_mode': planning_mode,
            'has_neighbor_data': neighbor_filepath is not None
        })
        
    except Exception as e:
        return jsonify({'error': f'文件上传失败: {str(e)}'}), 500

@app.route('/api/process/<task_id>', methods=['POST'])
def process_data(task_id):
    """处理数据接口"""
    try:
        if task_id not in task_queue:
            return jsonify({'error': '任务不存在'}), 404
        
        task = task_queue[task_id]
        if task['status'] != 'uploaded':
            return jsonify({'error': '任务状态错误'}), 400
        
        # 启动后台处理
        thread = Thread(target=process_pci_planning, args=(task_id,))
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'task_id': task_id,
            'status': 'processing',
            'message': '开始处理数据'
        })
        
    except Exception as e:
        return jsonify({'error': f'处理启动失败: {str(e)}'}), 500

def process_pci_planning(task_id):
    """后台处理PCI规划"""
    task = task_queue[task_id]
    
    try:
        # 更新状态
        task['status'] = 'processing'
        task['progress'] = 10
        task['message'] = '正在加载数据...'
        
        # 读取小区数据
        cell_file = task['cell_file']
        if cell_file.endswith('.csv'):
            df_cells = pd.read_csv(cell_file)
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
        else:
            df_cells = pd.read_excel(cell_file)
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
        
        task['cell_count'] = len(df_cells)
        task['progress'] = 20
        task['message'] = f'已加载{len(df_cells)}个小区数据'
        
        # 智能字段映射和修复
        task['progress'] = 30
        task['message'] = '正在智能处理数据字段...'

        from smart_field_mapper import SmartFieldMapper

        # 使用智能字段映射器
        mapper = SmartFieldMapper()
        df_cells, mapping_report = mapper.map_and_fix_fields(df_cells)

        # 打印映射报告
        print("智能字段映射报告:")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
        print(mapper.generate_report(mapping_report))  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print

        # 检查智能映射结果
        missing_fields = mapping_report['missing_fields']

        # 如果智能映射器无法解决所有问题，返回错误
        if missing_fields:
            task['status'] = 'failed'
            task['error'] = f'无法自动修复的字段: {", ".join(missing_fields)}。\n映射报告:\n{mapper.generate_report(mapping_report)}'
            return

        print(f"✅ 智能字段映射成功，所有必需字段已就绪")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
        
        # 超快速数据处理
        task['progress'] = 40
        task['message'] = '正在快速处理数据...'

        # 批量数据类型转换
        numeric_cols = ['latitude', 'longitude', 'pci', 'frequency', 'azimuth']
        for col in numeric_cols:
            df_cells[col] = pd.to_numeric(df_cells[col], errors='coerce')

        # 删除无效数据
        df_cells = df_cells.dropna(subset=numeric_cols)

        # 快速标准化
        df_cells['network_type'] = df_cells['network_type'].astype(str).str.upper()
        df_cells['network_type'] = df_cells['network_type'].replace({'LTE': '4G', 'NR': '5G'}).fillna('4G')
        df_cells['area_type'] = df_cells['area_type'].fillna('urban')

        print(f"数据处理完成: {len(df_cells)} 个小区")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
        
        # 读取邻区数据（如果有）
        neighbor_data = None
        if task.get('neighbor_file'):
            task['progress'] = 45
            task['message'] = '正在加载邻区数据...'

            neighbor_file = task['neighbor_file']
            if neighbor_file.endswith('.csv'):
                neighbor_data = pd.read_csv(neighbor_file)
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
            else:
                neighbor_data = pd.read_excel(neighbor_file)
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理

            print(f"邻区数据加载完成: {len(neighbor_data)} 条邻区关系")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print

        # 根据数据规模选择规划器
        task['progress'] = 50

        if len(df_cells) > 50000:
            task['message'] = '正在初始化极速PCI规划系统（大规模模式）...'
            from extreme_fast_pci_planner import ExtremeFastPCIPlanner
            planner = ExtremeFastPCIPlanner()
            task['message'] = '正在执行极速PCI规划（商业级性能）...'
        else:
            task['message'] = '正在初始化超高速PCI规划系统...'
            from ultra_fast_pci_planner import UltraFastPCIPlanner
            planner = UltraFastPCIPlanner()
            task['message'] = '正在执行超高速PCI规划...'

        # 执行PCI规划
        task['progress'] = 60

        planning_mode = task.get('planning_mode', 'problem_only')
        planning_result = planner.plan_pci(df_cells, neighbor_data, planning_mode)

        # 获取优化后的数据
        optimized_data = planning_result['optimized_data']

        # 保存优化后的数据
        optimized_file = os.path.join(app.config['RESULTS_FOLDER'], f'{task_id}_optimized_cells.csv')
        optimized_data.to_csv(optimized_file, index=False)

        # 快速生成可视化（仅在小数据集时）
        task['progress'] = 70
        task['message'] = '正在生成可视化...'

        map_file = os.path.join(app.config['RESULTS_FOLDER'], f'{task_id}_map.html')
        heatmap_file = os.path.join(app.config['RESULTS_FOLDER'], f'{task_id}_heatmap.png')

        if len(optimized_data) <= 500:  # 只为小数据集生成可视化
            try:
                system = UltraFastPCISystem()
                system.load_cells_from_csv(optimized_file)
                system.generate_visualization(map_file)
                system.generate_interference_heatmap(app.config['RESULTS_FOLDER'])

                # 重命名热力图文件
                if os.path.exists(os.path.join(app.config['RESULTS_FOLDER'], 'interference_heatmap.png')):
                    os.rename(
                        os.path.join(app.config['RESULTS_FOLDER'], 'interference_heatmap.png'),
                        heatmap_file
                    )
            except Exception as e:
                print(f"可视化生成失败: {e}")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
                # 创建占位文件
                with open(map_file, 'w') as f:
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
                    f.write('<html><body><h1>数据量过大，跳过可视化生成</h1></body></html>')
        else:
            # 大数据集跳过可视化
            with open(map_file, 'w') as f:
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
                f.write('<html><body><h1>数据量过大，跳过可视化生成</h1></body></html>')
        
        # 生成最终报告
        task['progress'] = 95
        task['message'] = '正在生成最终报告...'

        # 生成JSON报告（使用新的规划结果）
        json_report_file = os.path.join(app.config['RESULTS_FOLDER'], f'{task_id}_report.json')

        # 将规划结果转换为JSON格式
        report_data = {
            'task_id': task_id,
            'planning_mode': planning_mode,
            'has_neighbor_data': neighbor_data is not None,
            'summary': planning_result['report']['summary'],
            'original_issues': planning_result['report']['original_issues'],
            'optimized_issues': planning_result['report']['optimized_issues'],
            'improvements': planning_result['report']['improvements'],
            'pci_changes': planning_result['report']['pci_changes']
        }

        with open(json_report_file, 'w', encoding='utf-8') as f:
        # TODO: 添加适当的错误处理
        # TODO: 添加适当的错误处理
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        report = report_data

        # 生成Word分析报告
        task['message'] = '正在生成Word分析报告...'
        docx_report_file = os.path.join(app.config['RESULTS_FOLDER'], f'{task_id}_analysis_report.docx')
        docx_generated = False

        try:
            success = generate_pci_analysis_report(task_id, json_report_file, docx_report_file)
            if success and os.path.exists(docx_report_file):
                print(f"✅ Word分析报告生成成功: {docx_report_file}")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
                docx_generated = True
            else:
                print(f"⚠️ Word报告生成失败: 文件未创建")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
        except Exception as e:
            print(f"⚠️ Word报告生成失败: {e}")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
            # 即使Word报告生成失败，也不影响整体流程
        
        # 优化后的小区数据已经在前面保存了，这里不需要重复处理
        print(f"✅ 优化后的小区数据已保存: {optimized_file}")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
        
        # 完成处理
        task['status'] = 'completed'
        task['progress'] = 100
        task['message'] = '处理完成'
        task['completed_at'] = datetime.now().isoformat()

        # 构建结果，只包含成功生成的文件
        results = {
            'report': report,
            'map_file': f'{task_id}_map.html',
            'heatmap_file': f'{task_id}_heatmap.png',
            'optimized_file': f'{task_id}_optimized_cells.csv',
            'report_file': f'{task_id}_report.json'
        }

        # 只有在Word报告成功生成时才包含
        if docx_generated:
            results['analysis_report_file'] = f'{task_id}_analysis_report.docx'

        task['results'] = results
        
    except Exception as e:
        task['status'] = 'failed'
        task['error'] = str(e)
        task['message'] = f'处理失败: {str(e)}'

@app.route('/api/task/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    if task_id not in task_queue:
        return jsonify({'error': '任务不存在'}), 404
    
    task = task_queue[task_id]
    return jsonify(task)

@app.route('/api/tasks')
def list_tasks():
    """列出所有任务"""
    tasks = list(task_queue.values())
    tasks.sort(key=lambda x: x['created_at'], reverse=True)
    return jsonify({'tasks': tasks})

@app.route('/api/download/<task_id>/<file_type>')
def download_file(task_id, file_type):
    """下载文件"""
    # 直接检查文件是否存在，不依赖task_queue
    
    file_mapping = {
        'optimized': f'{task_id}_optimized_cells.csv',
        'report': f'{task_id}_analysis_report.docx',  # Word报告
        'json_report': f'{task_id}_report.json',      # JSON报告
        'map': f'{task_id}_map.html',
        'heatmap': f'{task_id}_heatmap.png'
    }
    
    if file_type not in file_mapping:
        return jsonify({'error': '无效的文件类型'}), 400
    
    filename = file_mapping[file_type]
    filepath = os.path.join(app.config['RESULTS_FOLDER'], filename)
    
    if not os.path.exists(filepath):
        return jsonify({
            'error': f'文件不存在: {filename}',
            'task_id': task_id,
            'file_type': file_type,
            'available_files': [f for f in os.listdir(app.config['RESULTS_FOLDER']) if f.startswith(task_id)]
        }), 404

    return send_file(filepath, as_attachment=True, download_name=filename)

@app.route('/api/view/<task_id>/<file_type>')
def view_file(task_id, file_type):
    """查看文件"""
    # 直接检查文件是否存在，不依赖task_queue
    if file_type == 'map':
        filename = f'{task_id}_map.html'
        filepath = os.path.join(app.config['RESULTS_FOLDER'], filename)
        if os.path.exists(filepath):
            return send_file(filepath)
    elif file_type == 'heatmap':
        filename = f'{task_id}_heatmap.png'
        filepath = os.path.join(app.config['RESULTS_FOLDER'], filename)
        if os.path.exists(filepath):
            return send_file(filepath, mimetype='image/png')

    # 如果文件不存在，返回友好的错误信息
    return jsonify({
        'error': f'文件不存在: {file_type}',
        'task_id': task_id,
        'available_files': [f for f in os.listdir(app.config['RESULTS_FOLDER']) if f.startswith(task_id)]
    }), 404

@app.route('/static/<path:filename>')
def static_files(filename):
    """静态文件服务"""
    return send_from_directory('static', filename)

@app.route('/test')
def test_page():
    """测试页面"""
    return send_file('test_map.html')

if __name__ == '__main__':
    print("🚀 启动PCI规划系统Web前端")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
    print("📍 访问地址: http://localhost:5000")  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
    print("=" * 50)  # TODO: 考虑使用logging代替print  # TODO: 考虑使用logging代替print
    app.run(host='0.0.0.0', port=5000, debug=True)
