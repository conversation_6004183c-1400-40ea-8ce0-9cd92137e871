#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地图编码修复工具
修复网络覆盖地图中的中文显示乱码问题
"""

import os
import re
from pathlib import Path

def fix_html_encoding(file_path):
    """修复HTML文件的编码问题"""
    try:
        # 尝试不同编码读取文件
        content = None
        original_encoding = None

        # 尝试的编码列表
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1', 'cp1252']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                original_encoding = encoding
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print(f"   ❌ 无法读取文件 {file_path}")
            return False

        # 如果原始编码不是UTF-8，需要转换
        if original_encoding != 'utf-8':
            print(f"   🔄 检测到 {original_encoding} 编码，转换为 UTF-8")

        # 确保HTML头部包含正确的编码声明
        if '<meta charset="utf-8">' not in content and '<meta charset="UTF-8">' not in content:
            # 检查是否有head标签
            if '<head>' in content.lower() or '<head ' in content.lower():
                # 在<head>标签后添加编码声明
                content = re.sub(
                    r'(<head[^>]*>)',
                    r'\1\n    <meta charset="utf-8">',
                    content,
                    flags=re.IGNORECASE
                )
            else:
                # 如果没有head标签，在html标签后添加
                if '<html>' in content.lower() or '<html ' in content.lower():
                    content = re.sub(
                        r'(<html[^>]*>)',
                        r'\1\n<head>\n    <meta charset="utf-8">\n</head>',
                        content,
                        flags=re.IGNORECASE
                    )
                else:
                    # 如果连html标签都没有，添加完整的HTML结构
                    content = f'<!DOCTYPE html>\n<html>\n<head>\n    <meta charset="utf-8">\n</head>\n<body>\n{content}\n</body>\n</html>'

        # 修复可能的编码问题字符
        encoding_fixes = {
            '�': '',  # 移除替换字符
            '\ufffd': '',  # 移除Unicode替换字符
            '&amp;': '&',  # 修复HTML实体
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'"
        }

        for old, new in encoding_fixes.items():
            content = content.replace(old, new)

        # 重新保存文件为UTF-8编码
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        return True

    except Exception as e:
        print(f"修复文件 {file_path} 失败: {e}")
        return False

def fix_map_files(results_dir="results"):
    """修复结果目录中的所有地图文件"""
    print("🔧 开始修复地图编码问题...")
    
    if not os.path.exists(results_dir):
        print(f"❌ 结果目录不存在: {results_dir}")
        return False
    
    fixed_count = 0
    total_count = 0
    
    # 查找所有HTML地图文件
    for file_path in Path(results_dir).glob("*.html"):
        total_count += 1
        print(f"   修复文件: {file_path.name}")
        
        if fix_html_encoding(file_path):
            fixed_count += 1
            print(f"   ✅ 修复成功")
        else:
            print(f"   ❌ 修复失败")
    
    print(f"\n📊 修复统计:")
    print(f"   总文件数: {total_count}")
    print(f"   修复成功: {fixed_count}")
    print(f"   修复率: {fixed_count/total_count*100:.1f}%" if total_count > 0 else "0%")
    
    return fixed_count == total_count

def create_test_map():
    """创建一个测试地图来验证编码修复"""
    print("\n🧪 创建测试地图验证编码...")
    
    try:
        import folium
        
        # 创建测试地图
        m = folium.Map(
            location=[40.0, 116.3],
            zoom_start=12,
            tiles='OpenStreetMap'
        )
        
        # 添加测试标记（使用英文避免编码问题）
        folium.Marker(
            location=[40.0, 116.3],
            popup="Test Cell - 4G Network",
            tooltip="Cell_001 (4G)",
            icon=folium.Icon(color='blue', icon='info-sign')
        ).add_to(m)
        
        folium.Marker(
            location=[40.01, 116.31],
            popup="Test Cell - 5G Network", 
            tooltip="Cell_002 (5G)",
            icon=folium.Icon(color='red', icon='info-sign')
        ).add_to(m)
        
        # 添加统计信息（使用英文）
        stats_html = """
        <div style="position: fixed;
                    top: 10px; left: 50px; width: 200px; height: 90px;
                    background-color: white; border:2px solid grey; z-index:9999;
                    font-size:14px; padding: 10px">
        <h4>Network Statistics</h4>
        <p><i class="fa fa-circle" style="color:blue"></i> 4G Cells: 1</p>
        <p><i class="fa fa-circle" style="color:red"></i> 5G Cells: 1</p>
        </div>
        """
        m.get_root().html.add_child(folium.Element(stats_html))
        
        # 保存测试地图
        test_file = "test_map_encoding.html"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(m._repr_html_())
        
        print(f"✅ 测试地图创建成功: {test_file}")
        
        # 验证文件编码
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if '�' in content or '\ufffd' in content:
                print("❌ 测试地图仍有编码问题")
                return False
            else:
                print("✅ 测试地图编码正常")
                return True
                
    except Exception as e:
        print(f"❌ 创建测试地图失败: {e}")
        return False

def validate_existing_maps(results_dir="results"):
    """验证现有地图文件的编码"""
    print("\n🔍 验证现有地图文件编码...")
    
    if not os.path.exists(results_dir):
        print(f"❌ 结果目录不存在: {results_dir}")
        return True  # 没有文件需要验证
    
    problem_files = []
    total_files = 0
    
    for file_path in Path(results_dir).glob("*.html"):
        total_files += 1
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查编码问题
            if '�' in content or '\ufffd' in content:
                problem_files.append(file_path.name)
                print(f"   ❌ {file_path.name}: 发现编码问题")
            else:
                print(f"   ✅ {file_path.name}: 编码正常")
                
        except UnicodeDecodeError:
            problem_files.append(file_path.name)
            print(f"   ❌ {file_path.name}: 无法读取（编码错误）")
        except Exception as e:
            print(f"   ⚠️ {file_path.name}: 检查失败 ({e})")
    
    print(f"\n📊 验证结果:")
    print(f"   总文件数: {total_files}")
    print(f"   问题文件: {len(problem_files)}")
    
    if problem_files:
        print(f"   问题文件列表: {', '.join(problem_files)}")
        return False
    else:
        print(f"   ✅ 所有文件编码正常")
        return True

def fix_web_frontend_encoding():
    """修复Web前端的编码设置"""
    print("\n🌐 检查Web前端编码设置...")
    
    web_file = "web_frontend.py"
    if not os.path.exists(web_file):
        print(f"❌ Web前端文件不存在: {web_file}")
        return False
    
    try:
        with open(web_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有编码相关的设置
        fixes_needed = []
        
        # 检查Flask应用的编码设置
        if "app.config['JSON_AS_ASCII'] = False" not in content:
            fixes_needed.append("添加JSON编码设置")
        
        if fixes_needed:
            print(f"   需要修复: {', '.join(fixes_needed)}")
            # 这里可以添加自动修复逻辑
            return False
        else:
            print(f"   ✅ Web前端编码设置正常")
            return True
            
    except Exception as e:
        print(f"❌ 检查Web前端失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 地图编码修复工具")
    print("=" * 50)
    
    # 1. 验证现有地图文件
    maps_ok = validate_existing_maps()
    
    # 2. 如果有问题，进行修复
    if not maps_ok:
        print("\n🔧 发现编码问题，开始修复...")
        fix_map_files()
        
        # 重新验证
        print("\n🔍 重新验证修复效果...")
        maps_ok = validate_existing_maps()
    
    # 3. 创建测试地图验证
    test_ok = create_test_map()
    
    # 4. 检查Web前端设置
    web_ok = fix_web_frontend_encoding()
    
    # 5. 总结
    print(f"\n🎯 修复总结:")
    print(f"   现有地图: {'✅ 正常' if maps_ok else '❌ 仍有问题'}")
    print(f"   测试地图: {'✅ 正常' if test_ok else '❌ 有问题'}")
    print(f"   Web前端: {'✅ 正常' if web_ok else '❌ 需要检查'}")
    
    if maps_ok and test_ok and web_ok:
        print(f"\n🎉 所有编码问题已修复！")
    else:
        print(f"\n⚠️ 仍有编码问题需要手动处理")
        
    # 清理测试文件
    test_file = "test_map_encoding.html"
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"🧹 已清理测试文件: {test_file}")
